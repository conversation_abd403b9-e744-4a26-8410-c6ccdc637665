import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import SelectPositionControl from '@/components/common/FormController/SelectPositionControl';
import { useGetUsersSearch } from '@/apis/users/users.api';
import { useFormContext } from 'react-hook-form';
import { Col, Row } from 'reactstrap';
import { useMemo, useEffect } from 'react';

const Seller = () => {
    const { watch, setValue } = useFormContext();
    const sellerStaffId = watch('seller.staffId');

    // Lấy danh sách users để tìm tên
    const { data: users = [] } = useGetUsersSearch({
        name: '', // Lấy tất cả users hoặc có thể để trống
    });

    // Tìm tên của staff được chọn
    const sellerStaffname = useMemo(() => {
        if (!sellerStaffId || !users.length) {
            return '';
        }
        const selectedUser = users.find(
            (user) => user.userId === sellerStaffId,
        );
        return selectedUser?.userName || '';
    }, [sellerStaffId, users]);

    // Tự động cập nhật tên staff vào form khi staffId thay đổi
    useEffect(() => {
        if (sellerStaffname) {
            setValue('seller.staffName', sellerStaffname);
        }
    }, [sellerStaffname, setValue]);

    // Bây giờ bạn có thể sử dụng:
    // - sellerStaffname: để lấy tên staff hiện tại
    // - watch('seller.staffName'): để lấy tên từ form
    // - watch('seller.staffId'): để lấy ID của staff
    return (
        <CollapseApp title='BÊN BÁN'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankName'
                            label='Ngân hàng'
                            placeholder='Nhập ngân hàng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankBranch'
                            label='Chi nhánh'
                            placeholder='Nhập chi nhánh'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankAccount'
                            label='Số tài khoản'
                            placeholder='Nhập số tài khoản'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <ComboboxSelectUserControl
                            name='seller.staffId'
                            label='Người đại diện'
                            placeholder='Chọn người đại diện'
                            style={{
                                width: '100%',
                            }}
                            required={true}
                        />
                        {/* Field ẩn để lưu tên staff */}
                        <FormController
                            controlType='textInput'
                            name='seller.staffName'
                            style={{ display: 'none' }}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <SelectPositionControl
                            name='seller.positionId'
                            label='Chức vụ'
                            placeholder='Chọn chức vụ'
                            required={true}
                            style={{
                                width: '100%',
                            }}
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default Seller;
